<img width="870" alt="image" src="https://github.com/partykit/partykit/assets/18808/485d32ff-cbee-4b03-8673-c594200774a2">

![npm beta](https://img.shields.io/npm/v/partykit)
[![Discord](https://img.shields.io/discord/1051830863576453180?color=7289DA&logo=discord&logoColor=white)](https://discord.gg/g5uqHQJc3z)
![License](https://img.shields.io/github/license/partykit/partykit)

[PartyKit](https://partykit.io/) simplifies developing multiplayer applications.

With PartyKit, you can focus on building multiplayer apps or adding real-time experiences to your existing projects with as little as one line of code. Meanwhile, PartyKit will handle operational complexity and real-time infrastructure scaling.

## Documentation

Go to [docs.partykit.io](https://docs.partykit.io) for documentation, guides and examples.

## Quick start

Note: to run PartyKit, you need to have Node v. 17 or higher installed.

You can create a PartyKit project by running:

```sh
npm create partykit@latest
```

This will ask a few questions about your project and create a new directory with a PartyKit application, that includes a server and a client.

Alternatively, you can add PartyKit to your existing project using the following command in the project's root directory:

```sh
npx partykit@latest init
```

From inside the directory, you can then run `npm run dev` to start the development server. When you'reready, you can deploy your application on to the PartyKit cloud with `npm run deploy`.

## Developers

- [Documentation](https://docs.partykit.io/) - [`/apps/docs`](./apps/docs)
- [Examples](https://docs.partykit.io/examples) - [`/apps/docs/examples`](./apps/docs/examples)
- [API References](https://docs.partykit.io/reference/) - [`/apps/docs/reference`](./apps/docs/reference)
- [Guides](https://docs.partykit.io/guides) - [`/apps/docs/guides`](./apps/docs/guides)
- [Blog](https://blog.partykit.io/) - [`/apps/blog`](./apps/blog)
- [Release Notes](https://github.com/partykit/partykit/releases)

## Community and support

- [GitHub issues](./issues) to report bugs 🐛
- [Discord](https://discord.gg/vwDWs68C) to ask questions, share your ideas and feedback, and help us celebrate your PartyKit projects 💕
- [Twitter](https://x.com/partykit) to say "hi" and get the freshest updates!

## Contributing

We encourage contributions to PartyKit. If you're interested in contributing or need help or have questions, please join us in our [Discord](https://discord.gg/g5uqHQJc3z).

## License

PartyKit is [MIT licensed](./LICENSE).
